#!/usr/bin/env node

// Database initialization script
// This script creates the database tables and sets up the initial schema

import { initializeDatabase, testConnection } from '../lib/db.js';
import { validateSMSConfig } from '../lib/sms.js';

async function initDB() {
  console.log('🚀 Starting database initialization...');
  
  try {
    // Test database connection
    console.log('📡 Testing database connection...');
    const connectionTest = await testConnection();
    if (!connectionTest) {
      throw new Error('Database connection failed');
    }
    console.log('✅ Database connection successful');
    
    // Initialize database schema
    console.log('🏗️  Creating database tables...');
    await initializeDatabase();
    console.log('✅ Database tables created successfully');
    
    // Validate SMS configuration
    console.log('📱 Validating SMS configuration...');
    validateSMSConfig();
    console.log('✅ SMS configuration validated');
    
    console.log('🎉 Database initialization completed successfully!');
    console.log('');
    console.log('Next steps:');
    console.log('1. Start your Next.js development server: npm run dev');
    console.log('2. Test the authentication system');
    console.log('3. Configure your SMS provider when ready');
    
  } catch (error) {
    console.error('❌ Database initialization failed:', error.message);
    console.error('');
    console.error('Troubleshooting:');
    console.error('1. Make sure MySQL is running');
    console.error('2. Check your database credentials in .env.local');
    console.error('3. Ensure the database "paranormal" exists');
    console.error('4. Verify database user has proper permissions');
    process.exit(1);
  }
}

// Run the initialization
initDB();
