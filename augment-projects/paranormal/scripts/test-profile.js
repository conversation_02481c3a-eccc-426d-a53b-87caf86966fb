#!/usr/bin/env node

// Profile system test script
// This script tests the profile functionality including balance display and profile updates

const BASE_URL = 'http://localhost:3000';

async function testProfileSystem() {
  console.log('🧪 Testing Profile System...\n');
  
  try {
    // Test data
    const testUser = {
      firstName: 'Profile',
      lastName: 'Test',
      email: '<EMAIL>',
      phone: '+995555987654',
      facebookUrl: 'https://facebook.com/profiletest',
      gender: 'female',
      age: 28,
      password: 'ProfileTest123',
      confirmPassword: 'ProfileTest123'
    };
    
    // Test 1: Register a new user
    console.log('1️⃣ Testing user registration with balance...');
    const registerResponse = await fetch(`${BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(testUser)
    });
    
    const registerData = await registerResponse.json();
    console.log('Registration response:', {
      success: registerData.success,
      user: {
        id: registerData.user?.id,
        name: registerData.user?.name,
        balance: registerData.user?.balance
      }
    });
    
    if (!registerData.success) {
      throw new Error(`Registration failed: ${registerData.message}`);
    }
    
    const token = registerData.token;
    console.log('✅ Registration successful with balance:', registerData.user.balance, '\n');
    
    // Test 2: Get current user to verify balance
    console.log('2️⃣ Testing get current user with balance...');
    const meResponse = await fetch(`${BASE_URL}/api/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const meData = await meResponse.json();
    console.log('Current user response:', {
      success: meData.success,
      balance: meData.user?.balance,
      name: meData.user?.name
    });
    
    if (!meData.success) {
      throw new Error(`Get current user failed: ${meData.message}`);
    }
    console.log('✅ Get current user successful with balance:', meData.user.balance, '\n');
    
    // Test 3: Update profile
    console.log('3️⃣ Testing profile update...');
    const updateData = {
      firstName: 'Updated',
      lastName: 'Profile',
      email: '<EMAIL>',
      facebookUrl: 'https://facebook.com/updatedprofile',
      gender: 'male',
      age: 30
    };
    
    const updateResponse = await fetch(`${BASE_URL}/api/auth/profile`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateData)
    });
    
    const updateResponseData = await updateResponse.json();
    console.log('Profile update response:', {
      success: updateResponseData.success,
      user: {
        name: updateResponseData.user?.name,
        email: updateResponseData.user?.email,
        balance: updateResponseData.user?.balance
      }
    });
    
    if (!updateResponseData.success) {
      throw new Error(`Profile update failed: ${updateResponseData.message}`);
    }
    console.log('✅ Profile update successful\n');
    
    // Test 4: Verify updated profile
    console.log('4️⃣ Testing get updated profile...');
    const updatedMeResponse = await fetch(`${BASE_URL}/api/auth/me`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const updatedMeData = await updatedMeResponse.json();
    console.log('Updated profile response:', {
      success: updatedMeData.success,
      user: {
        name: updatedMeData.user?.name,
        email: updatedMeData.user?.email,
        balance: updatedMeData.user?.balance,
        firstName: updatedMeData.user?.firstName,
        lastName: updatedMeData.user?.lastName
      }
    });
    
    if (!updatedMeData.success) {
      throw new Error(`Get updated profile failed: ${updatedMeData.message}`);
    }
    
    // Verify the updates
    const user = updatedMeData.user;
    if (user.firstName !== updateData.firstName || 
        user.lastName !== updateData.lastName ||
        user.email !== updateData.email) {
      throw new Error('Profile updates were not saved correctly');
    }
    
    console.log('✅ Profile updates verified successfully\n');
    
    // Test 5: Test validation errors
    console.log('5️⃣ Testing profile validation...');
    const invalidUpdateData = {
      firstName: 'A', // Too short
      lastName: '', // Empty
      email: 'invalid-email', // Invalid format
      gender: 'invalid', // Invalid value
      age: 15 // Too young
    };
    
    const invalidUpdateResponse = await fetch(`${BASE_URL}/api/auth/profile`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(invalidUpdateData)
    });
    
    const invalidUpdateData2 = await invalidUpdateResponse.json();
    console.log('Validation test response:', {
      success: invalidUpdateData2.success,
      hasErrors: !!invalidUpdateData2.errors,
      errorCount: Object.keys(invalidUpdateData2.errors || {}).length
    });
    
    if (invalidUpdateData2.success) {
      throw new Error('Validation should have failed but succeeded');
    }
    console.log('✅ Validation errors handled correctly\n');
    
    console.log('🎉 All profile tests passed!');
    console.log('\n📋 Test Summary:');
    console.log('✅ User Registration with Balance');
    console.log('✅ Get Current User with Balance');
    console.log('✅ Profile Update');
    console.log('✅ Profile Update Verification');
    console.log('✅ Profile Validation');
    
  } catch (error) {
    console.error('❌ Profile test failed:', error.message);
    process.exit(1);
  }
}

// Run the test
testProfileSystem();
