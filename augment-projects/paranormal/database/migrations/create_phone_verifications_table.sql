-- Create phone_verifications table for SMS verification
CREATE TABLE IF NOT EXISTS phone_verifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    phone VARCHAR(20) NOT NULL,
    verification_code VARCHAR(10) NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    
    -- Unique constraint to prevent multiple active verifications for same user
    UNIQUE KEY unique_user_phone (user_id, phone),
    
    -- Index for faster lookups
    INDEX idx_user_phone (user_id, phone),
    INDEX idx_expires_at (expires_at)
);

-- Add comment to table
ALTER TABLE phone_verifications COMMENT = 'Stores phone verification codes for phone number changes';
