'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../context/AuthContext';

export default function AuthModal({ isOpen, onClose, type, onSwitchType }) {
  const [currentStep, setCurrentStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState({});
  const [verificationCode, setVerificationCode] = useState('');
  const [isVerificationSent, setIsVerificationSent] = useState(false);
  const { login, register, verifyPhone } = useAuth();

  // Login form data
  const [loginData, setLoginData] = useState({
    phone: '',
    password: ''
  });

  // Registration form data
  const [registerData, setRegisterData] = useState({
    firstName: '',
    lastName: '',
    phone: '',
    facebookUrl: '',
    gender: '',
    password: '',
    confirmPassword: '',
    age: '',
    termsAccepted: false
  });

  useEffect(() => {
    if (isOpen) {
      setCurrentStep(1);
      setErrors({});
      setVerificationCode('');
      setIsVerificationSent(false);
      setIsLoading(false);
    }
  }, [isOpen, type]);



  const validatePhone = (phone) => {
    const phoneRegex = /^(\+995|995)?[0-9]{9}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
  };

  const validatePassword = (password) => {
    return password.length >= 8 && 
           /[A-Z]/.test(password) && 
           /[a-z]/.test(password) && 
           /[0-9]/.test(password);
  };

  const validateFacebookUrl = (url) => {
    if (!url) return true; // Optional field
    const facebookRegex = /^https?:\/\/(www\.)?facebook\.com\/[a-zA-Z0-9.]+$/;
    return facebookRegex.test(url);
  };

  const handleLoginSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setErrors({});

    const newErrors = {};
    if (!validatePhone(loginData.phone)) {
      newErrors.phone = 'გთხოვთ შეიყვანოთ სწორი ტელეფონის ნომერი';
    }
    if (!loginData.password) {
      newErrors.password = 'პაროლი აუცილებელია';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      setIsLoading(false);
      return;
    }

    const result = await login(loginData.phone, loginData.password);
    setIsLoading(false);

    if (result.success) {
      onClose();
    } else {
      setErrors({ general: 'არასწორი ტელეფონის ნომერი ან პაროლი' });
    }
  };

  const handleRegisterStep1 = (e) => {
    e.preventDefault();
    const newErrors = {};

    if (!registerData.firstName.trim()) {
      newErrors.firstName = 'სახელი აუცილებელია';
    }
    if (!registerData.lastName.trim()) {
      newErrors.lastName = 'გვარი აუცილებელია';
    }
    if (!validatePhone(registerData.phone)) {
      newErrors.phone = 'გთხოვთ შეიყვანოთ სწორი ტელეფონის ნომერი';
    }
    if (!validateFacebookUrl(registerData.facebookUrl)) {
      newErrors.facebookUrl = 'გთხოვთ შეიყვანოთ სწორი Facebook URL';
    }
    if (!registerData.gender) {
      newErrors.gender = 'სქესის მითითება აუცილებელია';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setErrors({});
    setCurrentStep(2);
  };

  const handleRegisterStep2 = async (e) => {
    e.preventDefault();
    const newErrors = {};

    if (!validatePassword(registerData.password)) {
      newErrors.password = 'პაროლი უნდა შეიცავდეს მინიმუმ 8 სიმბოლოს, დიდ და პატარა ასოებს და ციფრს';
    }
    if (registerData.password !== registerData.confirmPassword) {
      newErrors.confirmPassword = 'პაროლები არ ემთხვევა';
    }
    if (!registerData.termsAccepted) {
      newErrors.termsAccepted = 'წესებისა და პირობების მიღება აუცილებელია';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setIsLoading(true);
    setErrors({});

    // Complete registration first
    const userData = {
      firstName: registerData.firstName,
      lastName: registerData.lastName,
      phone: registerData.phone,
      facebookUrl: registerData.facebookUrl,
      gender: registerData.gender,
      age: registerData.age || null,
      password: registerData.password,
      confirmPassword: registerData.confirmPassword
    };

    const result = await register(userData);

    if (result.success) {
      setIsVerificationSent(true);
      setIsLoading(false);
      setCurrentStep(3); // Move to verification step
    } else {
      setIsLoading(false);
      setErrors({ general: result.error || 'რეგისტრაცია ვერ დასრულდა' });
    }
  };

  const handlePhoneVerification = async (e) => {
    e.preventDefault();
    if (verificationCode.length !== 6) {
      setErrors({ verification: 'გთხოვთ შეიყვანოთ 6-ნიშნა კოდი' });
      return;
    }

    setIsLoading(true);
    setErrors({});

    const verifyResult = await verifyPhone(verificationCode);

    if (verifyResult.success) {
      // Verification successful - registration is already complete
      setIsLoading(false);
      setCurrentStep(4); // Success step
      setTimeout(() => {
        onClose();
      }, 2000);
    } else {
      setIsLoading(false);
      setErrors({ verification: verifyResult.error || 'არასწორი კოდი' });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 modal-backdrop">
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full max-h-[90vh] overflow-y-auto animate-scale-in">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-2xl font-bold text-gray-800 dark:text-white">
            {type === 'login' ? 'შესვლა' : 'რეგისტრაცია'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Progress bar for registration */}
        {type === 'register' && (
          <div className="px-6 pt-4">
            <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400 mb-2">
              <span>ნაბიჯი {currentStep} / 4</span>
              <span>{Math.round((currentStep / 4) * 100)}%</span>
            </div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full progress-bar" 
                style={{ width: `${(currentStep / 4) * 100}%` }}
              ></div>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="p-6">
          {errors.general && (
            <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/20 border border-red-300 dark:border-red-700 rounded-lg text-red-700 dark:text-red-400 text-sm">
              {errors.general}
            </div>
          )}

          {type === 'login' ? (
            <form onSubmit={handleLoginSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  ტელეფონის ნომერი
                </label>
                <input
                  type="tel"
                  value={loginData.phone}
                  onChange={(e) => setLoginData({ ...loginData, phone: e.target.value })}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white ${
                    errors.phone ? 'input-error' : 'border-gray-300 dark:border-gray-600'
                  }`}
                  placeholder="+995 555 123 456"
                />
                {errors.phone && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.phone}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  პაროლი
                </label>
                <input
                  type="password"
                  value={loginData.password}
                  onChange={(e) => setLoginData({ ...loginData, password: e.target.value })}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white ${
                    errors.password ? 'input-error' : 'border-gray-300 dark:border-gray-600'
                  }`}
                  placeholder="••••••••"
                />
                {errors.password && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.password}</p>}
              </div>

              <button
                type="submit"
                disabled={isLoading}
                className="w-full bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg font-medium transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'შესვლა...' : 'შესვლა'}
              </button>

              <div className="text-center">
                <button
                  type="button"
                  onClick={() => onSwitchType('register')}
                  className="text-primary hover:text-primary-dark text-sm transition-colors"
                >
                  არ გაქვთ ანგარიში? დარეგისტრირდით
                </button>
              </div>
            </form>
          ) : (
            // Registration forms based on current step
            <>
              {currentStep === 1 && (
                <form onSubmit={handleRegisterStep1} className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                    პირადი ინფორმაცია
                  </h3>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        სახელი *
                      </label>
                      <input
                        type="text"
                        value={registerData.firstName}
                        onChange={(e) => setRegisterData({ ...registerData, firstName: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white ${
                          errors.firstName ? 'input-error' : 'border-gray-300 dark:border-gray-600'
                        }`}
                      />
                      {errors.firstName && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.firstName}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        გვარი *
                      </label>
                      <input
                        type="text"
                        value={registerData.lastName}
                        onChange={(e) => setRegisterData({ ...registerData, lastName: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white ${
                          errors.lastName ? 'input-error' : 'border-gray-300 dark:border-gray-600'
                        }`}
                      />
                      {errors.lastName && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.lastName}</p>}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      ტელეფონის ნომერი *
                    </label>
                    <input
                      type="tel"
                      value={registerData.phone}
                      onChange={(e) => setRegisterData({ ...registerData, phone: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white ${
                        errors.phone ? 'input-error' : 'border-gray-300 dark:border-gray-600'
                      }`}
                      placeholder="+995 555 123 456"
                    />
                    {errors.phone && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.phone}</p>}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Facebook პროფილი *
                    </label>
                    <input
                      type="url"
                      value={registerData.facebookUrl}
                      onChange={(e) => setRegisterData({ ...registerData, facebookUrl: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white ${
                        errors.facebookUrl ? 'input-error' : 'border-gray-300 dark:border-gray-600'
                      }`}
                      placeholder="https://facebook.com/yourprofile"
                    />
                    {errors.facebookUrl && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.facebookUrl}</p>}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        სქესი *
                      </label>
                      <select
                        value={registerData.gender}
                        onChange={(e) => setRegisterData({ ...registerData, gender: e.target.value })}
                        className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white ${
                          errors.gender ? 'input-error' : 'border-gray-300 dark:border-gray-600'
                        }`}
                      >
                        <option value="">აირჩიეთ</option>
                        <option value="male">მამრობითი</option>
                        <option value="female">მდედრობითი</option>
                        <option value="other">სხვა</option>
                      </select>
                      {errors.gender && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.gender}</p>}
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        ასაკი
                      </label>
                      <input
                        type="number"
                        min="16"
                        max="100"
                        value={registerData.age}
                        onChange={(e) => setRegisterData({ ...registerData, age: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white"
                      />
                    </div>
                  </div>

                  <button
                    type="submit"
                    className="w-full bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg font-medium transition-all duration-300"
                  >
                    შემდეგი ნაბიჯი
                  </button>

                  <div className="text-center">
                    <button
                      type="button"
                      onClick={() => onSwitchType('login')}
                      className="text-primary hover:text-primary-dark text-sm transition-colors"
                    >
                      უკვე გაქვთ ანგარიში? შედით
                    </button>
                  </div>
                </form>
              )}

              {currentStep === 2 && (
                <form onSubmit={handleRegisterStep2} className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                    პაროლის შექმნა
                  </h3>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      პაროლი *
                    </label>
                    <input
                      type="password"
                      value={registerData.password}
                      onChange={(e) => setRegisterData({ ...registerData, password: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white ${
                        errors.password ? 'input-error' : 'border-gray-300 dark:border-gray-600'
                      }`}
                      placeholder="••••••••"
                    />
                    {errors.password && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.password}</p>}
                    <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                      მინიმუმ 8 სიმბოლო, დიდი და პატარა ასოები, ციფრი
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      პაროლის დადასტურება *
                    </label>
                    <input
                      type="password"
                      value={registerData.confirmPassword}
                      onChange={(e) => setRegisterData({ ...registerData, confirmPassword: e.target.value })}
                      className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white ${
                        errors.confirmPassword ? 'input-error' : 'border-gray-300 dark:border-gray-600'
                      }`}
                      placeholder="••••••••"
                    />
                    {errors.confirmPassword && <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.confirmPassword}</p>}
                  </div>

                  <div className="flex items-start space-x-2">
                    <input
                      type="checkbox"
                      id="terms"
                      checked={registerData.termsAccepted}
                      onChange={(e) => setRegisterData({ ...registerData, termsAccepted: e.target.checked })}
                      className="mt-1 w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary"
                    />
                    <label htmlFor="terms" className="text-sm text-gray-700 dark:text-gray-300">
                      ვეთანხმები{' '}
                      <a href="#" className="text-primary hover:text-primary-dark">
                        წესებსა და პირობებს
                      </a>
                    </label>
                  </div>
                  {errors.termsAccepted && <p className="text-sm text-red-600 dark:text-red-400">{errors.termsAccepted}</p>}

                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={() => setCurrentStep(1)}
                      className="flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 py-2 px-4 rounded-lg font-medium transition-all duration-300"
                    >
                      უკან
                    </button>
                    <button
                      type="submit"
                      disabled={isLoading}
                      className="flex-1 bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg font-medium transition-all duration-300 disabled:opacity-50"
                    >
                      {isLoading ? 'იგზავნება...' : 'კოდის გაგზავნა'}
                    </button>
                  </div>
                </form>
              )}

              {currentStep === 3 && (
                <form onSubmit={handlePhoneVerification} className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-800 dark:text-white mb-4">
                    ტელეფონის დადასტურება
                  </h3>

                  <div className="text-center">
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                      დადასტურების კოდი გაიგზავნა ნომერზე:
                    </p>
                    <p className="font-semibold text-gray-800 dark:text-white mb-6">
                      {registerData.phone}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 text-center">
                      შეიყვანეთ 6-ნიშნა კოდი
                    </label>
                    <input
                      type="text"
                      maxLength="6"
                      value={verificationCode}
                      onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, ''))}
                      className={`verification-input w-full px-3 py-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent dark:bg-gray-700 dark:text-white ${
                        errors.verification ? 'input-error' : 'border-gray-300 dark:border-gray-600'
                      }`}
                      placeholder="123456"
                    />
                    {errors.verification && <p className="mt-1 text-sm text-red-600 dark:text-red-400 text-center">{errors.verification}</p>}
                  </div>

                  <div className="text-center">
                    <button
                      type="button"
                      className="text-sm text-primary hover:text-primary-dark transition-colors"
                      onClick={() => {
                        setIsVerificationSent(false);
                        // Resend code logic here
                        setTimeout(() => setIsVerificationSent(true), 1000);
                      }}
                    >
                      კოდის ხელახლა გაგზავნა
                    </button>
                  </div>

                  <div className="flex space-x-3">
                    <button
                      type="button"
                      onClick={() => setCurrentStep(2)}
                      className="flex-1 bg-gray-300 hover:bg-gray-400 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-gray-200 py-2 px-4 rounded-lg font-medium transition-all duration-300"
                    >
                      უკან
                    </button>
                    <button
                      type="submit"
                      disabled={isLoading || verificationCode.length !== 6}
                      className="flex-1 bg-primary hover:bg-primary-dark text-white py-2 px-4 rounded-lg font-medium transition-all duration-300 disabled:opacity-50"
                    >
                      {isLoading ? 'მოწმდება...' : 'დადასტურება'}
                    </button>
                  </div>
                </form>
              )}

              {currentStep === 4 && (
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto">
                    <svg className="w-8 h-8 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 dark:text-white">
                    რეგისტრაცია წარმატებით დასრულდა!
                  </h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    კეთილი იყოს თქვენი მობრძანება ჩვენს საზოგადოებაში
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
