'use client';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '../context/AuthContext';

export default function UserProfile({ user, isMobile = false }) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const { logout } = useAuth();
  const dropdownRef = useRef(null);

  // Safety check for user object
  if (!user) {
    return null;
  }

  // Ensure user has required properties with defaults
  const safeUser = {
    name: user.name || `${user.firstName || ''} ${user.lastName || ''}`.trim() || 'User',
    isVerified: user.isVerified || false,
    avatar: user.avatar || null,
    balance: user.balance || 0,
    ...user
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleLogout = () => {
    logout();
    setIsDropdownOpen(false);
  };

  const navigateToTrips = () => {
    setIsDropdownOpen(false);
    // TODO: Navigate to user trips page when implemented
    console.log('Navigate to user trips');
  };

  const getInitials = (name) => {
    if (!name || typeof name !== 'string') {
      return 'U'; // Default to 'U' for User
    }

    return name
      .split(' ')
      .filter(word => word.length > 0) // Filter out empty strings
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2) || 'U'; // Fallback to 'U' if no valid initials
  };

  if (isMobile) {
    return (
      <div className="space-y-2">
        <div className="flex items-center px-3 py-2">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-semibold">
              {safeUser.avatar ? (
                <img src={safeUser.avatar} alt={safeUser.name} className="w-8 h-8 rounded-full object-cover" />
              ) : (
                getInitials(safeUser.name)
              )}
            </div>
            <div>
              <p className="text-sm font-medium text-gray-800 dark:text-white">{safeUser.name}</p>
              <p className="text-xs text-green-600 dark:text-green-400 font-medium">
                ბალანსი: ₾{safeUser.balance.toFixed(2)}
              </p>
            </div>
          </div>
        </div>
        
        <Link
          href="/profile"
          onClick={() => setIsDropdownOpen(false)}
          className="block w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors"
        >
          პროფილი
        </Link>

        <button
          onClick={navigateToTrips}
          className="block w-full text-left px-3 py-2 text-sm text-gray-700 dark:text-gray-300 hover:text-primary dark:hover:text-primary transition-colors"
        >
          ჩემი გასვლები
        </button>
        
        <button
          onClick={handleLogout}
          className="block w-full text-left px-3 py-2 text-sm text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 transition-colors"
        >
          გასვლა
        </button>
      </div>
    );
  }

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center space-x-2 text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 dark:focus:ring-offset-gray-800 transition-all duration-200"
      >
        <div className="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-semibold hover:bg-primary-dark transition-colors">
          {safeUser.avatar ? (
            <img src={safeUser.avatar} alt={safeUser.name} className="w-8 h-8 rounded-full object-cover" />
          ) : (
            getInitials(safeUser.name)
          )}
        </div>
        <span className="text-gray-700 dark:text-gray-300 font-medium hidden lg:block">
          {safeUser.name.split(' ')[0] || 'User'}
        </span>
        <svg 
          className={`w-4 h-4 text-gray-500 dark:text-gray-400 transition-transform duration-200 ${
            isDropdownOpen ? 'rotate-180' : ''
          }`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isDropdownOpen && (
        <div className="absolute right-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg ring-1 ring-black ring-opacity-5 dark:ring-gray-700 animate-scale-in z-50">
          <div className="py-1">
            {/* User Info */}
            <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
              <p className="text-sm font-medium text-gray-800 dark:text-white">{safeUser.name}</p>
              <p className="text-sm text-gray-500 dark:text-gray-400 truncate">{safeUser.email}</p>
              <p className="text-sm text-green-600 dark:text-green-400 font-medium mt-1">
                ბალანსი: ₾{safeUser.balance.toFixed(2)}
              </p>
              {!safeUser.isVerified && (
                <p className="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                  ⚠️ ტელეფონი არ არის დადასტურებული
                </p>
              )}
            </div>

            {/* Menu Items */}
            <Link
              href="/profile"
              onClick={() => setIsDropdownOpen(false)}
              className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span>პროფილი</span>
              </div>
            </Link>

            <button
              onClick={navigateToTrips}
              className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>ჩემი გასვლები</span>
              </div>
            </button>

            <Link
              href="/profile?tab=security"
              onClick={() => setIsDropdownOpen(false)}
              className="block w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              <div className="flex items-center space-x-2">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <span>პარამეტრები</span>
              </div>
            </Link>

            <div className="border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={handleLogout}
                className="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20 transition-colors"
              >
                <div className="flex items-center space-x-2">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                  <span>გასვლა</span>
                </div>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
