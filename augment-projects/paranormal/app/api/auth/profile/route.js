import { NextResponse } from 'next/server';
import { verifyAuthToken } from '../../../../lib/jwt.js';
import { getUserById } from '../../../../lib/auth.js';
import { sanitizeUser } from '../../../../lib/validation.js';
import { query } from '../../../../lib/db.js';
import <PERSON><PERSON> from 'joi';

// Profile update validation schema
const profileUpdateSchema = Joi.object({
  firstName: Joi.string()
    .min(2)
    .max(50)
    .required()
    .messages({
      'string.min': 'First name must be at least 2 characters',
      'string.max': 'First name cannot exceed 50 characters',
      'any.required': 'First name is required'
    }),
    
  lastName: Joi.string()
    .min(2)
    .max(50)
    .required()
    .messages({
      'string.min': 'Last name must be at least 2 characters',
      'string.max': 'Last name cannot exceed 50 characters',
      'any.required': 'Last name is required'
    }),
    

    
  facebookUrl: Joi.string()
    .uri()
    .allow(null, '')
    .messages({
      'string.uri': 'Please provide a valid Facebook URL'
    }),
    
  gender: Joi.string()
    .valid('male', 'female', 'other')
    .allow('', null)
    .messages({
      'any.only': 'Gender must be male, female, or other'
    }),
    
  age: Joi.alternatives()
    .try(
      Joi.number().integer().min(16).max(100),
      Joi.string().allow(''),
      Joi.allow(null)
    )
    .messages({
      'number.min': 'Age must be at least 16',
      'number.max': 'Age cannot exceed 100'
    })
});

export async function PUT(request) {
  try {
    // Verify JWT token
    let userPayload;
    try {
      userPayload = verifyAuthToken(request);
    } catch (error) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Authentication required',
          errors: { auth: 'Invalid or expired token' }
        },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate input data
    const { error, value } = profileUpdateSchema.validate(body, {
      abortEarly: false,
      stripUnknown: true
    });
    
    if (error) {
      const errors = {};
      error.details.forEach(detail => {
        errors[detail.path[0]] = detail.message;
      });
      return NextResponse.json(
        { 
          success: false, 
          message: 'Validation failed', 
          errors 
        },
        { status: 400 }
      );
    }

    const updateData = value;

    // Convert empty strings to null for database
    const dbData = {
      firstName: updateData.firstName || null,
      lastName: updateData.lastName || null,
      facebookUrl: updateData.facebookUrl || null,
      gender: updateData.gender || null,
      age: updateData.age === '' || updateData.age === null ? null : parseInt(updateData.age) || null
    };

    // Update user profile
    await query(
      `UPDATE users SET
        first_name = ?,
        last_name = ?,
        facebook_url = ?,
        gender = ?,
        age = ?,
        updated_at = CURRENT_TIMESTAMP
      WHERE id = ?`,
      [
        dbData.firstName,
        dbData.lastName,
        dbData.facebookUrl,
        dbData.gender,
        dbData.age,
        userPayload.id
      ]
    );

    // Get updated user data
    const updatedUser = await getUserById(userPayload.id);
    if (!updatedUser) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'User not found' 
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Profile updated successfully',
      user: sanitizeUser(updatedUser)
    });

  } catch (error) {
    console.error('Profile update error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Profile update failed. Please try again.',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}

export async function POST() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}
