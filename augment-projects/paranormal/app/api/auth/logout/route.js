import { NextResponse } from 'next/server';
import { verifyAuthToken } from '../../../../lib/jwt.js';

export async function POST(request) {
  try {
    // Verify JWT token (optional for logout, but good for logging)
    let userPayload = null;
    try {
      userPayload = verifyAuthToken(request);
    } catch (error) {
      // Token might be invalid/expired, but we still allow logout
      console.log('Logout with invalid/expired token');
    }
    
    // In a stateless JWT system, logout is primarily handled on the client side
    // by removing the token from storage. However, we can log the logout event
    // and potentially add the token to a blacklist if needed.
    
    if (userPayload) {
      console.log(`User ${userPayload.id} logged out`);
      
      // TODO: Optional - Add token to blacklist table for enhanced security
      // This would require checking the blacklist on every authenticated request
      // For now, we rely on client-side token removal
    }
    
    return NextResponse.json({
      success: true,
      message: 'Logged out successfully'
    });
    
  } catch (error) {
    console.error('Logout error:', error);
    
    // Even if there's an error, we should allow logout to succeed
    // since the client will remove the token anyway
    return NextResponse.json({
      success: true,
      message: 'Logged out successfully'
    });
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}
