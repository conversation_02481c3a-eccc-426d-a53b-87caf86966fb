import { NextResponse } from 'next/server';
import { verifyAuthToken } from '../../../../lib/jwt.js';
import { getUserById } from '../../../../lib/auth.js';
import { sanitizeUser } from '../../../../lib/validation.js';

export async function GET(request) {
  try {
    // Verify JWT token
    let userPayload;
    try {
      userPayload = verifyAuthToken(request);
    } catch (error) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Authentication required',
          errors: { auth: 'Invalid or expired token' }
        },
        { status: 401 }
      );
    }
    
    // Get current user data from database
    const user = await getUserById(userPayload.id);
    if (!user) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'User not found' 
        },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      user: sanitizeUser(user)
    });
    
  } catch (error) {
    console.error('Get current user error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to get user data',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function POST() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}
