import { NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/jwt';
import { query } from '../../../../lib/db';
import { validateData, sanitizeUser } from '../../../../lib/validation';
import { getUserById } from '../../../../lib/auth';
import Joi from 'joi';

// Phone verification schema
const verifyPhoneSchema = Joi.object({
  phone: Joi.string()
    .pattern(/^5\d{8}$/)
    .required()
    .messages({
      'string.pattern.base': 'Phone number must be in format 5XXXXXXXX',
      'any.required': 'Phone number is required'
    }),
  verificationCode: Joi.string()
    .length(6)
    .pattern(/^\d{6}$/)
    .required()
    .messages({
      'string.length': 'Verification code must be 6 digits',
      'string.pattern.base': 'Verification code must contain only digits',
      'any.required': 'Verification code is required'
    })
});

export async function POST(request) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'Authorization token required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const userPayload = verifyToken(token);
    
    if (!userPayload) {
      return NextResponse.json(
        { success: false, message: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate input data
    const validation = validateData(verifyPhoneSchema, body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Validation failed', 
          errors: validation.errors 
        },
        { status: 400 }
      );
    }
    
    const { phone, verificationCode } = validation.data;

    // Get verification record
    const verificationRecord = await query(
      `SELECT * FROM phone_verifications 
       WHERE user_id = ? AND phone = ? AND expires_at > CURRENT_TIMESTAMP 
       ORDER BY created_at DESC LIMIT 1`,
      [userPayload.id, phone]
    );
    
    if (verificationRecord.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Verification code expired or not found',
          errors: { verificationCode: 'Verification code expired or not found' }
        },
        { status: 400 }
      );
    }

    const record = verificationRecord[0];
    
    // In development mode, accept any 6-digit code
    const isValidCode = process.env.NODE_ENV === 'development' 
      ? verificationCode.length === 6 && /^\d{6}$/.test(verificationCode)
      : record.verification_code === verificationCode;
    
    if (!isValidCode) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Invalid verification code',
          errors: { verificationCode: 'Invalid verification code' }
        },
        { status: 400 }
      );
    }

    // Update user's phone number
    await query(
      'UPDATE users SET phone = ?, is_verified = 1, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [phone, userPayload.id]
    );

    // Delete used verification code
    await query(
      'DELETE FROM phone_verifications WHERE user_id = ? AND phone = ?',
      [userPayload.id, phone]
    );

    // Get updated user data
    const updatedUser = await getUserById(userPayload.id);
    if (!updatedUser) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'User not found' 
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Phone number updated successfully',
      user: sanitizeUser(updatedUser)
    });

  } catch (error) {
    console.error('Verify phone change error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Phone verification failed. Please try again.',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}
