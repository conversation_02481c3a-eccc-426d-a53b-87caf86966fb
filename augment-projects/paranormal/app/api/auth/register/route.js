import { NextResponse } from 'next/server';
import { registerSchema, validateData, sanitizeUser } from '../../../../lib/validation.js';
import { createUser, getUserByPhone } from '../../../../lib/auth.js';
import { generateToken, createUserPayload } from '../../../../lib/jwt.js';
import { sendVerificationCode } from '../../../../lib/sms.js';

export async function POST(request) {
  try {
    const body = await request.json();
    
    // Validate input data
    const validation = validateData(registerSchema, body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Validation failed', 
          errors: validation.errors 
        },
        { status: 400 }
      );
    }
    
    const userData = validation.data;

    // Check if user already exists by phone
    const existingUserByPhone = await getUserByPhone(userData.phone);
    if (existingUserByPhone) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'User with this phone number already exists',
          errors: { phone: 'Phone number already registered' }
        },
        { status: 409 }
      );
    }
    
    // Create user
    const result = await createUser(userData);
    
    // Send verification code
    try {
      await sendVerificationCode(userData.phone, result.verificationCode);
    } catch (smsError) {
      console.error('SMS sending failed:', smsError);
      // Don't fail registration if SMS fails, just log it
    }
    
    // Generate JWT token for the new user
    const userPayload = createUserPayload({
      id: result.id,
      first_name: userData.firstName,
      last_name: userData.lastName,
      email: userData.email,
      phone: userData.phone,
      is_verified: false
    });
    
    const token = generateToken(userPayload);
    
    return NextResponse.json({
      success: true,
      message: 'User registered successfully. Please verify your phone number.',
      user: sanitizeUser({
        id: result.id,
        first_name: userData.firstName,
        last_name: userData.lastName,
        email: userData.email,
        phone: userData.phone,
        facebook_url: userData.facebookUrl,
        gender: userData.gender,
        age: userData.age,
        is_verified: false,
        created_at: new Date(),
        updated_at: new Date()
      }),
      token,
      requiresVerification: true
    }, { status: 201 });
    
  } catch (error) {
    console.error('Registration error:', error);

    if (error.message === 'Phone number already exists') {
      return NextResponse.json(
        {
          success: false,
          message: 'Phone number already registered',
          errors: { phone: 'Phone number already registered' }
        },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Registration failed. Please try again.',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}
