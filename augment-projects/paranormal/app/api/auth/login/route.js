import { NextResponse } from 'next/server';
import { loginSchema, validateData, sanitizeUser } from '../../../../lib/validation.js';
import { getUserByPhone, verifyPassword } from '../../../../lib/auth.js';
import { generateToken, createUserPayload } from '../../../../lib/jwt.js';

export async function POST(request) {
  try {
    const body = await request.json();
    
    // Validate input data
    const validation = validateData(loginSchema, body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Validation failed', 
          errors: validation.errors 
        },
        { status: 400 }
      );
    }
    
    const { phone, password } = validation.data;

    // Find user by phone
    const user = await getUserByPhone(phone);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid phone number or password',
          errors: { general: 'Invalid phone number or password' }
        },
        { status: 401 }
      );
    }
    
    // Verify password
    const isPasswordValid = await verifyPassword(password, user.password_hash);
    if (!isPasswordValid) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid phone number or password',
          errors: { general: 'Invalid phone number or password' }
        },
        { status: 401 }
      );
    }
    
    // Generate JWT token
    const userPayload = createUserPayload(user);
    const token = generateToken(userPayload);
    
    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Login successful',
      user: sanitizeUser(user),
      token
    });
    
  } catch (error) {
    console.error('Login error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Login failed. Please try again.',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}
