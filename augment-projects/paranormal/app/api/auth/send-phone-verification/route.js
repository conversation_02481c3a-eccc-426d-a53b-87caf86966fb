import { NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/jwt';
import { query } from '../../../../lib/db';
import { validateData } from '../../../../lib/validation';
import <PERSON><PERSON> from 'joi';

// Phone verification schema
const phoneVerificationSchema = Joi.object({
  phone: Joi.string()
    .pattern(/^5\d{8}$/)
    .required()
    .messages({
      'string.pattern.base': 'Phone number must be in format 5XXXXXXXX',
      'any.required': 'Phone number is required'
    })
});

export async function POST(request) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, message: 'Authorization token required' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);
    const userPayload = verifyToken(token);
    
    if (!userPayload) {
      return NextResponse.json(
        { success: false, message: 'Invalid or expired token' },
        { status: 401 }
      );
    }

    const body = await request.json();
    
    // Validate input data
    const validation = validateData(phoneVerificationSchema, body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Validation failed', 
          errors: validation.errors 
        },
        { status: 400 }
      );
    }
    
    const { phone } = validation.data;

    // Check if phone number already exists for another user
    const existingUser = await query(
      'SELECT id FROM users WHERE phone = ? AND id != ?',
      [phone, userPayload.id]
    );
    
    if (existingUser.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Phone number already exists',
          errors: { phone: 'This phone number is already registered to another account' }
        },
        { status: 409 }
      );
    }

    // Generate verification code (6 digits)
    const verificationCode = Math.floor(100000 + Math.random() * 900000).toString();
    
    // Store verification code in database with expiration (10 minutes)
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes from now
    
    await query(
      `INSERT INTO phone_verifications (user_id, phone, verification_code, expires_at, created_at) 
       VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
       ON DUPLICATE KEY UPDATE 
       phone = VALUES(phone),
       verification_code = VALUES(verification_code), 
       expires_at = VALUES(expires_at),
       created_at = CURRENT_TIMESTAMP`,
      [userPayload.id, phone, verificationCode, expiresAt]
    );

    // In development mode, accept any 6-digit code
    if (process.env.NODE_ENV === 'development') {
      console.log(`Development mode: Phone verification code for ${phone}: ${verificationCode}`);
    } else {
      // TODO: Send SMS with verification code
      // This is where you would integrate with SMS provider like Twilio, etc.
      console.log(`SMS would be sent to ${phone} with code: ${verificationCode}`);
    }

    return NextResponse.json({
      success: true,
      message: 'Verification code sent successfully',
      // In development, return the code for testing
      ...(process.env.NODE_ENV === 'development' && { verificationCode })
    });

  } catch (error) {
    console.error('Send phone verification error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to send verification code. Please try again.',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}
