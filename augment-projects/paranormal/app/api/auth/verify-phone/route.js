import { NextResponse } from 'next/server';
import { phoneVerificationSchema, validateData, sanitizeUser } from '../../../../lib/validation.js';
import { verifyUserPhone, getUserById } from '../../../../lib/auth.js';
import { verifyAuthToken, generateToken, createUserPayload } from '../../../../lib/jwt.js';
import { query } from '../../../../lib/db.js';

export async function POST(request) {
  try {
    const body = await request.json();
    
    // Validate input data
    const validation = validateData(phoneVerificationSchema, body);
    if (!validation.isValid) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Validation failed', 
          errors: validation.errors 
        },
        { status: 400 }
      );
    }
    
    const { code } = validation.data;
    
    // Verify JWT token to get user ID
    let userPayload;
    try {
      userPayload = verifyAuthToken(request);
    } catch (error) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'Authentication required',
          errors: { auth: 'Please login first' }
        },
        { status: 401 }
      );
    }
    
    // Verify phone number
    try {
      await verifyUserPhone(userPayload.id, code);
    } catch (error) {
      // Development bypass: Accept any 6-digit code in development mode
      if (process.env.NODE_ENV === 'development' &&
          error.message === 'Invalid verification code') {

        console.log('🔧 Development Mode: Phone verification bypass activated');
        console.log(`📱 User ${userPayload.id} bypassed verification with code: ${code}`);

        // Manually update verification status for bypass
        await query(
          `UPDATE users SET
            is_verified = TRUE,
            phone_verification_code = NULL,
            phone_verification_expires_at = NULL
          WHERE id = ?`,
          [userPayload.id]
        );

        console.log('✅ Development Mode: User verification status updated via bypass');
      } else {
        // Production behavior: Handle verification errors normally
        if (error.message === 'Invalid verification code') {
          return NextResponse.json(
            {
              success: false,
              message: 'Invalid verification code',
              errors: { code: 'Invalid verification code' }
            },
            { status: 400 }
          );
        }

        if (error.message === 'Verification code expired') {
          return NextResponse.json(
            {
              success: false,
              message: 'Verification code expired',
              errors: { code: 'Verification code expired. Please request a new one.' }
            },
            { status: 400 }
          );
        }

        throw error;
      }
    }
    
    // Get updated user data
    const updatedUser = await getUserById(userPayload.id);
    if (!updatedUser) {
      return NextResponse.json(
        { 
          success: false, 
          message: 'User not found' 
        },
        { status: 404 }
      );
    }
    
    // Generate new token with updated verification status
    const newUserPayload = createUserPayload(updatedUser);
    const newToken = generateToken(newUserPayload);
    
    return NextResponse.json({
      success: true,
      message: 'Phone number verified successfully',
      user: sanitizeUser(updatedUser),
      token: newToken
    });
    
  } catch (error) {
    console.error('Phone verification error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Verification failed. Please try again.',
        error: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}

// Handle unsupported methods
export async function GET() {
  return NextResponse.json(
    { success: false, message: 'Method not allowed' },
    { status: 405 }
  );
}
