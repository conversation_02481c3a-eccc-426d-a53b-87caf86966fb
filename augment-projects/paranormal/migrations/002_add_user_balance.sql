-- Add balance field to users table
ALTER TABLE users ADD COLUMN balance DECIMAL(10,2) DEFAULT 0.00 AFTER age;

-- Add index for balance queries
ALTER TABLE users ADD INDEX idx_balance (balance);

-- Create transactions table for balance history
CREATE TABLE IF NOT EXISTS user_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('credit', 'debit') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description VARCHAR(255) NOT NULL,
    reference_type ENUM('trip_payment', 'refund', 'admin_adjustment', 'bonus') DEFAULT NULL,
    reference_id INT DEFAULT NULL,
    balance_before DECIMAL(10,2) NOT NULL,
    balance_after DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_type (type),
    INDEX idx_created_at (created_at),
    INDEX idx_reference (reference_type, reference_id)
);
