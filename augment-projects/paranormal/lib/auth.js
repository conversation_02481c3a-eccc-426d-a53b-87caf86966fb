import bcrypt from 'bcryptjs';
import { query } from './db.js';

// Hash password
export async function hashPassword(password) {
  try {
    const saltRounds = 12;
    return await bcrypt.hash(password, saltRounds);
  } catch (error) {
    console.error('Password hashing error:', error);
    throw new Error('Failed to hash password');
  }
}

// Verify password
export async function verifyPassword(password, hashedPassword) {
  try {
    return await bcrypt.compare(password, hashedPassword);
  } catch (error) {
    console.error('Password verification error:', error);
    throw new Error('Failed to verify password');
  }
}

// Generate verification code
export function generateVerificationCode() {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Format phone number to Georgian standard
export function formatPhoneNumber(phone) {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Handle different formats
  if (cleaned.startsWith('995')) {
    // Already has country code
    return '+' + cleaned;
  } else if (cleaned.startsWith('5')) {
    // Georgian mobile number without country code
    return '+995' + cleaned;
  } else if (cleaned.length === 9 && cleaned.startsWith('5')) {
    // 9-digit Georgian mobile
    return '+995' + cleaned;
  } else {
    // Assume it needs Georgian country code
    return '+995' + cleaned;
  }
}

// Validate phone number
export function validatePhoneNumber(phone) {
  const formatted = formatPhoneNumber(phone);
  // Georgian mobile numbers: +995 5XX XXX XXX or +995 7XX XXX XXX
  const georgianMobileRegex = /^\+995[57]\d{8}$/;
  return georgianMobileRegex.test(formatted);
}



// Check if user exists by phone
export async function getUserByPhone(phone) {
  try {
    const formattedPhone = formatPhoneNumber(phone);
    const users = await query(
      'SELECT * FROM users WHERE phone = ?',
      [formattedPhone]
    );
    return users.length > 0 ? users[0] : null;
  } catch (error) {
    console.error('Get user by phone error:', error);
    throw new Error('Failed to fetch user');
  }
}

// Get user by ID
export async function getUserById(id) {
  try {
    const users = await query(
      'SELECT * FROM users WHERE id = ?',
      [id]
    );
    return users.length > 0 ? users[0] : null;
  } catch (error) {
    console.error('Get user by ID error:', error);
    throw new Error('Failed to fetch user');
  }
}

// Create new user
export async function createUser(userData) {
  try {
    const {
      firstName,
      lastName,
      phone,
      facebookUrl,
      gender,
      age,
      password
    } = userData;

    const hashedPassword = await hashPassword(password);
    const formattedPhone = formatPhoneNumber(phone);
    const verificationCode = generateVerificationCode();
    const verificationExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Convert undefined values to null for database compatibility
    const facebookUrlValue = facebookUrl || null;
    const ageValue = age || null;

    const result = await query(
      `INSERT INTO users (
        first_name, last_name, phone, facebook_url,
        gender, age, password_hash, phone_verification_code,
        phone_verification_expires_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        firstName,
        lastName,
        formattedPhone,
        facebookUrlValue,
        gender,
        ageValue,
        hashedPassword,
        verificationCode,
        verificationExpires
      ]
    );

    return {
      id: result.insertId,
      verificationCode
    };
  } catch (error) {
    console.error('Create user error:', error);
    if (error.code === 'ER_DUP_ENTRY') {
      if (error.message.includes('phone')) {
        throw new Error('Phone number already exists');
      }
    }
    throw new Error('Failed to create user');
  }
}

// Update user verification status
export async function verifyUserPhone(userId, code) {
  try {
    const user = await getUserById(userId);
    if (!user) {
      throw new Error('User not found');
    }

    if (user.phone_verification_code !== code) {
      throw new Error('Invalid verification code');
    }

    if (new Date() > new Date(user.phone_verification_expires_at)) {
      throw new Error('Verification code expired');
    }

    await query(
      `UPDATE users SET 
        is_verified = TRUE, 
        phone_verification_code = NULL, 
        phone_verification_expires_at = NULL 
      WHERE id = ?`,
      [userId]
    );

    return true;
  } catch (error) {
    console.error('Verify user phone error:', error);
    throw error;
  }
}
