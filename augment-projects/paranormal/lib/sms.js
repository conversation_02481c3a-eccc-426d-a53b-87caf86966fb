// SMS Service - Placeholder implementation for future SMS provider integration
// This service is designed to be easily replaceable with real SMS providers

const SMS_PROVIDER = process.env.SMS_PROVIDER || 'mock';
const SMS_FROM_NUMBER = process.env.SMS_FROM_NUMBER || '+995555000000';

// Mock SMS service for development/testing
class MockSMSService {
  async sendSMS(to, message) {
    console.log('📱 Mock SMS Service');
    console.log(`To: ${to}`);
    console.log(`From: ${SMS_FROM_NUMBER}`);
    console.log(`Message: ${message}`);
    console.log('---');
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    return {
      success: true,
      messageId: `mock_${Date.now()}`,
      provider: 'mock'
    };
  }
  
  async sendVerificationCode(phone, code) {
    const message = `Your verification code for Paranormal Adventures is: ${code}. This code will expire in 10 minutes.`;
    return await this.sendSMS(phone, message);
  }
}

// Placeholder for Twilio SMS service
class TwilioSMSService {
  constructor() {
    this.accountSid = process.env.TWILIO_ACCOUNT_SID;
    this.authToken = process.env.TWILIO_AUTH_TOKEN;
    this.fromNumber = process.env.TWILIO_FROM_NUMBER || SMS_FROM_NUMBER;
  }
  
  async sendSMS(to, message) {
    // TODO: Implement Twilio integration
    // const twilio = require('twilio')(this.accountSid, this.authToken);
    // 
    // try {
    //   const result = await twilio.messages.create({
    //     body: message,
    //     from: this.fromNumber,
    //     to: to
    //   });
    //   
    //   return {
    //     success: true,
    //     messageId: result.sid,
    //     provider: 'twilio'
    //   };
    // } catch (error) {
    //   console.error('Twilio SMS error:', error);
    //   throw new Error('Failed to send SMS');
    // }
    
    throw new Error('Twilio SMS service not implemented yet');
  }
  
  async sendVerificationCode(phone, code) {
    const message = `Your verification code for Paranormal Adventures is: ${code}. This code will expire in 10 minutes.`;
    return await this.sendSMS(phone, message);
  }
}

// Placeholder for other SMS providers
class GenericSMSService {
  constructor() {
    this.apiKey = process.env.SMS_API_KEY;
    this.apiUrl = process.env.SMS_API_URL;
    this.fromNumber = SMS_FROM_NUMBER;
  }
  
  async sendSMS(to, message) {
    // TODO: Implement generic SMS provider integration
    throw new Error('Generic SMS service not implemented yet');
  }
  
  async sendVerificationCode(phone, code) {
    const message = `Your verification code for Paranormal Adventures is: ${code}. This code will expire in 10 minutes.`;
    return await this.sendSMS(phone, message);
  }
}

// SMS Service Factory
function createSMSService() {
  switch (SMS_PROVIDER.toLowerCase()) {
    case 'twilio':
      return new TwilioSMSService();
    case 'generic':
      return new GenericSMSService();
    case 'mock':
    default:
      return new MockSMSService();
  }
}

// Main SMS service instance
const smsService = createSMSService();

// Public API
export async function sendVerificationCode(phone, code) {
  try {
    console.log(`Sending verification code to ${phone}: ${code}`);
    const result = await smsService.sendVerificationCode(phone, code);
    console.log('SMS sent successfully:', result);
    return result;
  } catch (error) {
    console.error('Failed to send verification code:', error);
    throw error;
  }
}

export async function sendSMS(to, message) {
  try {
    return await smsService.sendSMS(to, message);
  } catch (error) {
    console.error('Failed to send SMS:', error);
    throw error;
  }
}

// Utility function to validate SMS provider configuration
export function validateSMSConfig() {
  const provider = SMS_PROVIDER.toLowerCase();
  
  switch (provider) {
    case 'twilio':
      if (!process.env.TWILIO_ACCOUNT_SID || !process.env.TWILIO_AUTH_TOKEN) {
        throw new Error('Twilio configuration missing: TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN required');
      }
      break;
    case 'generic':
      if (!process.env.SMS_API_KEY || !process.env.SMS_API_URL) {
        throw new Error('Generic SMS configuration missing: SMS_API_KEY and SMS_API_URL required');
      }
      break;
    case 'mock':
      console.log('Using mock SMS service for development');
      break;
    default:
      console.warn(`Unknown SMS provider: ${provider}, falling back to mock`);
  }
  
  return true;
}
