# Authentication System Documentation

## Overview

This document describes the complete authentication backend system implemented for the Paranormal Investigator's Outdoor Activities website. The system provides secure user registration, login, phone verification, and JWT-based session management.

## Features

- ✅ **User Registration** with multi-step form support
- ✅ **Secure Login** with email and password
- ✅ **JWT Token Authentication** for session management
- ✅ **Phone Number Verification** (SMS ready, currently using mock service)
- ✅ **User Profile Management** with edit functionality
- ✅ **User Balance System** with transaction tracking
- ✅ **Password Hashing** with bcrypt
- ✅ **Input Validation** with Joi schemas
- ✅ **MySQL Database Integration** with connection pooling
- ✅ **Georgian Phone Number Support** (+995 format)
- ✅ **Production-Ready Architecture** with proper error handling

## Database Schema

### Users Table
```sql
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHA<PERSON>(100) NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20) NOT NULL UNIQUE,
    facebook_url VARCHAR(500),
    gender ENUM('male', 'female', 'other') NOT NULL,
    age INT,
    balance DECIMAL(10,2) DEFAULT 0.00,
    password_hash VARCHAR(255) NOT NULL,
    is_verified BOOLEAN DEFAULT FALSE,
    phone_verification_code VARCHAR(6),
    phone_verification_expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### User Transactions Table
```sql
CREATE TABLE user_transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('credit', 'debit') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    description VARCHAR(255) NOT NULL,
    reference_type ENUM('trip_payment', 'refund', 'admin_adjustment', 'bonus') DEFAULT NULL,
    reference_id INT DEFAULT NULL,
    balance_before DECIMAL(10,2) NOT NULL,
    balance_after DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API Endpoints

### POST /api/auth/register
Register a new user account.

**Request Body:**
```json
{
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "+************",
  "facebookUrl": "https://facebook.com/johndoe",
  "gender": "male",
  "age": 25,
  "password": "SecurePassword123",
  "confirmPassword": "SecurePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully. Please verify your phone number.",
  "user": { ... },
  "token": "jwt_token_here",
  "requiresVerification": true
}
```

### POST /api/auth/login
Authenticate user with email and password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "user": { ... },
  "token": "jwt_token_here"
}
```

### POST /api/auth/verify-phone
Verify phone number with SMS code.

**Headers:**
```
Authorization: Bearer jwt_token_here
```

**Request Body:**
```json
{
  "code": "123456"
}
```

**Development Mode:**
- In development (`NODE_ENV=development`), any 6-digit numeric code will be accepted
- This allows testing the complete registration flow without SMS provider
- Bypass actions are logged for debugging
- Production behavior remains unchanged

### GET /api/auth/me
Get current authenticated user information.

**Headers:**
```
Authorization: Bearer jwt_token_here
```

### POST /api/auth/logout
Logout current user.

**Headers:**
```
Authorization: Bearer jwt_token_here
```

### PUT /api/auth/profile
Update user profile information.

**Headers:**
```
Authorization: Bearer jwt_token_here
```

**Request Body:**
```json
{
  "firstName": "Updated",
  "lastName": "Name",
  "email": "<EMAIL>",
  "facebookUrl": "https://facebook.com/updated",
  "gender": "male",
  "age": 30
}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "user": { ... }
}
```

## Environment Configuration

Create a `.env.local` file with the following variables:

```env
# Database Configuration
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=paranormal
DB_USERNAME=root
DB_PASSWORD=rootpass

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# SMS Configuration (Placeholder for future implementation)
SMS_PROVIDER=mock
SMS_API_KEY=placeholder-key
SMS_FROM_NUMBER=+995555000000

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development
```

## Setup Instructions

1. **Install Dependencies:**
   ```bash
   npm install
   ```

2. **Configure Environment:**
   - Copy `.env.local` and update database credentials
   - Ensure MySQL is running and database exists

3. **Initialize Database:**
   ```bash
   npm run init-db
   ```

4. **Start Development Server:**
   ```bash
   npm run dev
   ```

5. **Test Authentication System:**
   ```bash
   npm run test-auth        # Basic authentication tests
   npm run test-profile     # Profile management tests
   npm run test-complete    # Complete end-to-end flow test
   ```

## Development Features

### Phone Verification Bypass

For development and testing purposes, the phone verification system includes a bypass mechanism:

- **Environment:** Only active when `NODE_ENV=development`
- **Functionality:** Any 6-digit numeric code will be accepted for verification
- **Validation:** Still validates code format (must be exactly 6 digits, numbers only)
- **Logging:** All bypass actions are logged for debugging
- **Security:** Automatically disabled in production environments

**Usage:**
```javascript
// In development, any of these codes will work:
{ "code": "123456" }
{ "code": "999888" }
{ "code": "000000" }
```

**Server Logs:**
```
🔧 Development Mode: Phone verification bypass activated
📱 User 3 bypassed verification with code: 123456
✅ Development Mode: User verification status updated via bypass
```

## SMS Provider Integration

The system is designed to easily integrate with SMS providers. Currently using a mock service for development.

### To Add Real SMS Provider:

1. **For Twilio:**
   ```env
   SMS_PROVIDER=twilio
   TWILIO_ACCOUNT_SID=your_account_sid
   TWILIO_AUTH_TOKEN=your_auth_token
   TWILIO_FROM_NUMBER=+**********
   ```

2. **For Generic Provider:**
   ```env
   SMS_PROVIDER=generic
   SMS_API_KEY=your_api_key
   SMS_API_URL=https://api.provider.com/sms
   ```

3. **Update `lib/sms.js`** to implement the provider-specific logic.

## Frontend Integration

The authentication system integrates with the existing frontend through the `AuthContext`:

```javascript
import { useAuth } from './context/AuthContext';

function MyComponent() {
  const { user, login, register, logout, verifyPhone, isAuthenticated } = useAuth();
  
  // Use authentication functions
}
```

## Security Features

- **Password Hashing:** bcrypt with 12 salt rounds
- **JWT Tokens:** Signed with secret, 7-day expiration
- **Input Validation:** Comprehensive validation with Joi
- **SQL Injection Protection:** Parameterized queries
- **Phone Number Validation:** Georgian mobile number format
- **Error Handling:** Secure error messages without sensitive data exposure

## Testing

Run the authentication test suite:
```bash
npm run test-auth
```

This tests all endpoints and ensures the system is working correctly.

## Production Deployment

1. **Update Environment Variables:**
   - Change `JWT_SECRET` to a strong, unique value
   - Configure real SMS provider
   - Update database credentials

2. **Database Security:**
   - Use dedicated database user with minimal permissions
   - Enable SSL connections
   - Regular backups

3. **Additional Security:**
   - Implement rate limiting
   - Add CORS configuration
   - Enable HTTPS
   - Consider token blacklisting for enhanced security

## Troubleshooting

### Common Issues:

1. **Database Connection Failed:**
   - Check MySQL is running
   - Verify credentials in `.env.local`
   - Ensure database exists

2. **JWT Token Issues:**
   - Check `JWT_SECRET` is set
   - Verify token format in requests

3. **SMS Not Working:**
   - Check SMS provider configuration
   - Verify phone number format

### Debug Mode:
Set `NODE_ENV=development` to see detailed error messages.
